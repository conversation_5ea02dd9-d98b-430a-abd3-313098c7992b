import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'

export default class Import<PERSON>ob extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public partnerId: number

  @column()
  public userId: number | null

  @column()
  public status: 'pending' | 'processing' | 'completed' | 'failed_with_errors' | 'failed'

  @column()
  public totalRows: number

  @column()
  public processedRows: number

  @column()
  public originalFilename: string

  @column()
  public storedFilePath: string

  @column()
  public errorsReport: any | null

  @column()
  public completedAt: DateTime | null

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
