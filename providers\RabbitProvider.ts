import type { ApplicationContract } from '@ioc:Adonis/Core/Application'
import Env from '@ioc:Adonis/Core/Env'
import amqplib from 'amqplib'
import ProcessPatientImportResponse from 'App/Services/RabbitQueue/ProcessPatientImportResponse'

export default class RabbitProvider {
	constructor(protected app: ApplicationContract) { }

	public register() {
		if (Env.get('AMQP_ACTIVE') !== 'development') {
			this.app.container.singleton('Rabbit', async () => {
				const conn = await amqplib.connect(Env.get('CLOUD_AMQP_URL'))
				return conn
			})
		}
	}

	public async boot() {
		if (Env.get('AMQP_ACTIVE') !== 'development') {
			this.app.container.singleton('RabbitChannel', async () => {
				const conn = await this.app.container.resolveBinding('Rabbit')
				const channel = await conn.createChannel()
				return channel
			})
		}
	}

	public async ready() {
		if (Env.get('AMQP_ACTIVE') !== 'development') {
			const channel = await this.app.container.resolveBinding('RabbitChannel')

			const queuePatientImport = 'patient_import_queue'
			await channel.assertQueue(queuePatientImport, {
				durable: true
			})
			channel.prefetch(1)
			await channel.consume(queuePatientImport, async (msg) => {
				if (!msg) return;

				try {
					const payload = JSON.parse(msg.content.toString())
					// Delega o trabalho pesado para o nosso novo serviço
					await ProcessPatientImportResponse(payload)
				} catch (error) {
					console.error('Erro ao processar mensagem da fila patient_import_queue', error)
				} finally {
					// Garante que a mensagem seja confirmada (removida da fila) mesmo se houver erro
					channel.ack(msg)
				}
			})
		}
	}

	public async shutdown() {
		if (Env.get('AMQP_ACTIVE') !== 'development') {
			const conn = await this.app.container.resolveBinding('Rabbit')
			const channel = await this.app.container.resolveBinding('RabbitChannel')
			channel.close()
			conn.close()
		}
	}
}
