// app/Services/RabbitQueue/ProcessPatientImportResponse.ts

import { DateTime } from 'luxon'

// Tipagem do payload que vem da fila
type ProcessPatientImportPayload = {
  jobId: number;
}

// A função principal que será exportada
export default async function ProcessPatientImportResponse(payload: ProcessPatientImportPayload) {
  // Usamos imports dinâmicos para evitar problemas de dependência circular, seguindo seu padrão
  const ImportJob = (await import('App/Models/ImportJob')).default
  const User = (await import('App/Models/User')).default
  const UserInfo = (await import('App/Models/UserInfo')).default
  const Role = (await import('App/Models/Role')).default
  const Database = (await import('@ioc:Adonis/Lucid/Database')).default
  const Drive = (await import('@ioc:Adonis/Core/Drive')).default
  const ExcelJS = (await import('exceljs')).default
  const Logger = (await import('@ioc:Adonis/Core/Logger')).default

  const job = await ImportJob.findOrFail(payload.jobId)

  try {
    await job.merge({ status: 'processing' }).save()
    Logger.info(`Processando Job de Importação #${job.id}`)

    const fileStream = await Drive.getStream('s3', job.storedFilePath)
    const workbook = new ExcelJS.stream.xlsx.WorkbookReader(fileStream)

    const allRowsData = []
    let headerRow = []

    for await (const worksheetReader of workbook) {
      for await (const row of worksheetReader) {
        // Captura o cabeçalho para um mapeamento mais robusto
        if (row.number === 1) {
          headerRow = row.values as string[]
          continue;
        }
        // Mapeia os valores da linha para um objeto usando o cabeçalho
        const rowData = {}
        row.values.forEach((value, i) => {
          const header = headerRow[i]
          if (header) rowData[header] = value
        })

        // Normaliza os dados como no seu front-end original
        const newCell = (rowData['Celular']?.toString() || '').replace(/\D/g, "")
        allRowsData.push({
          name: rowData['Nome'] || '',
          email: rowData['E-mail']?.toString().toLowerCase().trim() || '',
          legalDocumentNumber: (rowData['CPF']?.toString() || '').replace(/\D/g, ''),
          dddCell: newCell?.slice(0, 2),
          cell: newCell?.slice(2),
          birthDate: rowData['Data de Nascimento/Fundação (##/##/####)'],
          password: rowData['Senha (Não obrigatório)'] || null,
          holderLegalDocumentNumber: (rowData['CPF titular (Não obrigatório)']?.toString() || '').replace(/\D/g, ''),
          rowNumber: row.number,
        })
      }
    }

    await job.merge({ totalRows: allRowsData.length }).save()

    const holderUsersData = allRowsData.filter(u => !u.holderLegalDocumentNumber)
    const dependentUsersData = allRowsData.filter(u => !!u.holderLegalDocumentNumber)
    const errorsReport: { row: number; message: string; }[] = []
    let processedCount = 0

    // === SUA LÓGICA DE NEGÓCIO ORIGINAL, AGORA NO LUGAR CERTO ===
    const trx = await Database.transaction()
    try {
      const createdHolderPatients: User[] = []
      const patientRole = await Role.findBy('slug', 'patient')

      // 1. Processa os titulares
      for (const holder of holderUsersData) {
        let patient = await User.query({ client: trx }).where('email', holder.email).preload('userInfo').first()

        if (patient) { // Atualiza o usuário existente
          patient.merge({ password: holder.password || holder.legalDocumentNumber, is_active: true })
          await patient.useTransaction(trx).save()
          patient.userInfo.merge({ name: holder.name, dddCell: holder.dddCell, cell: holder.cell, birthDate: holder.birthDate })
          await patient.userInfo.useTransaction(trx).save()
        } else { // Cria um novo usuário
          patient = new User()
          await patient.useTransaction(trx).merge({ email: holder.email, password: holder.password || holder.legalDocumentNumber, is_active: true, type: 'patient' }).save()
          const userInfo = new UserInfo()
          await userInfo.useTransaction(trx).merge({ userId: patient.id, name: holder.name, legalDocumentNumber: holder.legalDocumentNumber, dddCell: holder.dddCell, cell: holder.cell, birthDate: holder.birthDate }).save()
          patient.$setRelated('userInfo', userInfo)
          if (patientRole) await patient.related('roles').attach([patientRole.id], trx)
        }
        createdHolderPatients.push(patient)
        // Associa o paciente ao parceiro
        await trx.table('partner_patients').insert({ partner_id: job.partnerId, patient_id: patient.id }).onConflict(['partner_id', 'patient_id']).ignore()
      }

      // 2. Processa os dependentes
      for (const dependent of dependentUsersData) {
        const parent = createdHolderPatients.find(p => p.userInfo.legalDocumentNumber === dependent.holderLegalDocumentNumber)
        if (!parent) {
          errorsReport.push({ row: dependent.rowNumber, message: `CPF do titular ${dependent.holderLegalDocumentNumber} não foi encontrado nesta planilha.` })
          continue
        }
        // ... lógica para criar ou atualizar o dependente, usando `parent.id` ...
      }

      await trx.commit()
      processedCount = holderUsersData.length + dependentUsersData.length - errorsReport.length
    } catch (error) {
      await trx.rollback()
      Logger.error(`Erro na transação do Job #${job.id}`, error)
      throw new Error('Erro ao processar lote no banco de dados. A operação foi revertida.')
    }
    // === FIM DA LÓGICA DE NEGÓCIO ===

    await job.merge({
      status: errorsReport.length > 0 ? 'failed_with_errors' : 'completed',
      processedRows: processedCount,
      errorsReport: errorsReport.length > 0 ? errorsReport : null,
      completedAt: DateTime.now(),
    }).save()

    // Notifica o usuário sobre o fim do processo, como no seu exemplo
    const adminUser = await User.find(job.userId)
    if (adminUser) {
      // Lógica para criar notificação no banco e enviar via WebSocket
      // Exemplo: await NotificationUser.create({ ... })
      Ws.io.to(`user:${adminUser.secureId}`).emit('import_finished', job)
    }

    await Drive.delete('s3', job.storedFilePath)
    Logger.info(`Job de Importação #${job.id} finalizado com status: ${job.status}`)

  } catch (err) {
    Logger.error(`Falha crítica no Job de Importação #${job.id}`, err)
    await job.merge({ status: 'failed', errorsReport: { critical: err.message } }).save()
  }
}